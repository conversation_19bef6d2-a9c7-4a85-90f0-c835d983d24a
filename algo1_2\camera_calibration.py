import cv2
import numpy as np
import yaml
import os
from glob import glob

# -------------------------- 1. 配置参数（需用户根据硬件调整）--------------------------
CHESSBOARD_SIZE = (6, 4)  # 棋盘格内角点数量（列x行）
SQUARE_SIZE = 160  # 棋盘格每个方格的物理尺寸（单位：mm）
CHESSBOARD_IMGS_PATH = r"D:\Dataset\keystone/"  # 棋盘格图像路径
SAVE_PARAMS_PATH = "./camera_params.yml"  # 内参保存路径

# -------------------------- 2. 生成棋盘格3D世界坐标 --------------------------
# 世界坐标系：棋盘格平面为Z=0，内角点坐标为 (i*SQUARE_SIZE, j*SQUARE_SIZE, 0)
objp = np.zeros((CHESSBOARD_SIZE[0] * CHESSBOARD_SIZE[1], 3), np.float32)
objp[:, :2] = np.mgrid[0:CHESSBOARD_SIZE[0], 0:CHESSBOARD_SIZE[1]].T.reshape(-1, 2)
objp *= SQUARE_SIZE  # 缩放为物理尺寸

# 存储所有图像的3D世界点和2D图像点
obj_points = []  # 3D世界坐标
img_points = []  # 2D图像坐标

# -------------------------- 3. 读取棋盘格图像并检测角点 --------------------------
img_paths = ["cam_1080p_1.jpg", "cam_1080p_2.jpg", "cam_1080p_3.jpg", "cam_1080p_4.jpg"]
img_paths = [os.path.join(CHESSBOARD_IMGS_PATH, img_path) for img_path in img_paths]

corners_dict = {
    "cam_1080p_1.jpg": np.array([[443, 339], [481, 339], [517, 339], [553, 339], [588, 340], [621, 340], [444, 374], [482, 375], [520, 373], [556, 373], [591, 374], [625, 373], [447, 411], [485, 411], [522, 410], [558, 409], [595, 408], [629, 410], [449, 449], [488, 448], [525, 447], [563, 446], [597, 444], [633, 444]],dtype=np.float32),

    "cam_1080p_2.jpg": np.array([[174, 425], [213, 425], [252, 426], [289, 427], [326, 428], [363, 430], [169, 460], [209, 460], [248, 462], [287, 462], [325, 463], [361, 464], [164, 497], [207, 497], [245, 498], [284, 498], [322, 499], [359, 499], [161, 534], [202, 535], [242, 535], [281, 534], [319, 535], [357, 535]],dtype=np.float32),

    "cam_1080p_3.jpg": np.array([[280, 294], [315, 296], [347, 300], [379, 304], [410, 306], [442, 309], [277, 322], [311, 326], [344, 328], [376, 332], [410, 334], [439, 337], [273, 350], [308, 354], [342, 358], [374, 359], [406, 362], [438, 364], [268, 382], [305, 383], [339, 386], [371, 389], [405, 391], [437, 394]],dtype=np.float32),

    "cam_1080p_4.jpg": np.array([[26, 422], [64, 422], [105, 422], [145, 423], [182, 423], [219, 422], [23, 458], [62, 456], [101, 456], [141, 457], [180, 457], [218, 456], [17, 493], [59, 493], [99, 493], [137, 492], [177, 490], [217, 491], [14, 529], [56, 529], [97, 528], [137, 527], [175, 526], [215, 525]],dtype=np.float32),
}

for img_path in img_paths:
    img = cv2.imread(img_path)
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    
    # 检测棋盘格内角点（亚像素级精度）
    #ret, corners = cv2.findChessboardCorners(gray, CHESSBOARD_SIZE, None)
    ret = True
    corners = corners_dict[os.path.basename(img_path)]
    if ret:
        obj_points.append(objp)
        # 亚像素优化（提高角点检测精度）
        corners_subpix = cv2.cornerSubPix(
            gray, corners, (11, 11), (-1, -1),
            criteria=(cv2.TERM_CRITERIA_EPS + cv2.TERM_CRITERIA_MAX_ITER, 30, 0.001)
        )
        img_points.append(corners_subpix)
        
        # 绘制角点并显示（可选）
        img_draw = cv2.drawChessboardCorners(img, CHESSBOARD_SIZE, corners_subpix, ret)
        cv2.imshow("Chessboard Corners", cv2.resize(img_draw, (800, 600)))
        cv2.waitKey(500)

cv2.destroyAllWindows()

# -------------------------- 4. 校准相机并保存内参 --------------------------
if len(obj_points) > 0:
    # 调用OpenCV校准函数，获取内参、畸变系数等
    ret, mtx, dist, rvecs, tvecs = cv2.calibrateCamera(
        obj_points, img_points, gray.shape[::-1], None, None
    )
    print("obj_points", obj_points)
    print("img_points", img_points)

    
    # 保存参数到YAML文件
    params = {
        "camera_matrix": mtx.tolist(),
        "dist_coeffs": dist.tolist(),
        "image_size": gray.shape[::-1]  # (width, height)
    }
    with open(SAVE_PARAMS_PATH, "w") as f:
        yaml.dump(params, f)
    
    print("相机内参校准完成！")
    print(f"内参矩阵 A:\n{mtx}")
    print(f"畸变系数 dist:\n{dist}")
else:
    print("未检测到足够的棋盘格角点，请重新拍摄图像！")