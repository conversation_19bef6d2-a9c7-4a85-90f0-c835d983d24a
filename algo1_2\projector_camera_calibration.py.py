import cv2
import numpy as np
import yaml
import os

# -------------------------- 1. 加载相机内参 --------------------------
def load_camera_params(params_path):
    with open(params_path, "r") as f:
        params = yaml.safe_load(f)
    return (
        np.array(params["camera_matrix"]),
        np.array(params["dist_coeffs"]),
        tuple(params["image_size"])
    )

camera_matrix, dist_coeffs, img_size = load_camera_params("./camera_params.yml")

# -------------------------- 2. 配置参数（需用户调整）--------------------------
CARDBOARD_SIZE = (1920 // 5, 1080 // 5)  # 纸板物理尺寸（宽×高，单位：mm，论文参数）
PROJECTOR_RES = (1920, 1080)  # 投影仪分辨率（宽×高）
NUM_CALIB_POINTS = 18  # 校准点数量（≥8，论文建议至少8个）
SAVE_GP_PATH = "./projector_Gp.yml"  # Gp保存路径

# 纸板平面方程（假设初始时纸板在相机坐标系下的平面为 Z = Z0，需手动测量或通过棋盘格校准）
# 这里通过棋盘格预先获取纸板平面：将纸板与棋盘格重合，用solvePnP求平面方程
def get_cardboard_plane(imgpath):
    # 纸板4个角的世界坐标（纸板坐标系：原点在左上，X向右，Y向下，Z=0）
    # cardboard_3d = np.array([
    #     [0, 0, 0],
    #     [CARDBOARD_SIZE[0], 0, 0],
    #     [CARDBOARD_SIZE[0], CARDBOARD_SIZE[1], 0],
    #     [0, CARDBOARD_SIZE[1], 0]
    # ], dtype=np.float32)

    cardboard_3d_dict = {"cam_1080p_12.jpg": np.array([
                [0, 0, 0],
                [1131, 0, 0],
                [1131, 636, 0],
                [0, 636, 0]
            ], dtype=np.float32),
            "cam_1080p_14.jpg": np.array([
                [0, 0, 0],
                [1706, 0, 0],
                [1706, 960, 0],
                [0, 960, 0]
            ], dtype=np.float32),
    }

    cardboard_3d = cardboard_3d_dict[imgpath] #cam_1080p_12.jpg
    corners_2d_dict = {"cam_1080p_11.jpg": np.array([[366, 217], [856, 212],[882, 493],[335, 489]], dtype=np.float32),
                    "cam_1080p_10.jpg": np.array([[378, 218], [870, 213],[894, 492],[349, 490]], dtype=np.float32),
                    "cam_1080p_12.jpg": np.array([[148, 106], [1072, 91],[1162, 649],[43, 638]], dtype=np.float32),
                    "cam_1080p_14.jpg": np.array([[174, 107], [1096, 93],[1194, 649],[76, 638]], dtype=np.float32),
                }
    corners_2d = corners_2d_dict[imgpath] #cam_1080p_11.jpg
    
    # 用solvePnP求纸板平面方程 ax + by + cz + d = 0
    ret, rvec, tvec = cv2.solvePnP(cardboard_3d, corners_2d, camera_matrix, dist_coeffs)
    R, _ = cv2.Rodrigues(rvec)  # 旋转矩阵
    # 平面法向量 = R的第三列（纸板坐标系Z轴→相机坐标系）
    n = R[:, 2]
    a, b, c = n
    d = -np.dot(n, tvec.flatten())  # 平面方程：aX + bY + cZ + d = 0
    return np.array([a, b, c, d])


ic_list_dict = {"cam_1080p_11.jpg": [[467, 293], [522, 293], [580, 293], [636, 292], [695, 293], [753, 292],
            [463, 348], [520, 345], [579, 346], [636, 346], [696, 346], [755, 346],
            [459, 403], [518, 403], [576, 403], [636, 403], [697, 403], [757, 403]],
        "cam_1080p_10.jpg": [[478, 293], [535, 293], [593, 293], [650, 293], [708, 293], [764, 292], 
            [476, 348], [532, 347], [590, 346], [649, 346], [709, 347], [768, 346],  
            [472, 403], [530, 403], [590, 403], [650, 403], [709, 403], [770, 403]],
        "cam_1080p_12.jpg": [[325, 244], [433, 243], [541, 242], [651, 241], [762, 240], [875, 239], 
            [313, 346], [423, 345], [536, 345], [651, 345], [766, 344], [883, 343], 
            [299, 457], [415, 458], [532, 457], [651, 457], [771, 457], [893, 458]],
        "cam_1080p_14.jpg": [[356, 247], [462, 245], [571, 243], [681, 243], [791, 242], [903, 240],
            [343, 348], [455, 348], [567, 346], [682, 347], [797, 346], [914, 345],
            [331, 458], [447, 458], [565, 459], [683, 459], [804, 460], [925, 460]]
}
# 获取纸板平面方程
plane_eq = get_cardboard_plane("cam_1080p_12.jpg")  # [a, b, c, d]
print(f"纸板平面方程：{plane_eq[0]}X + {plane_eq[1]}Y + {plane_eq[2]}Z + {plane_eq[3]} = 0")

# -------------------------- 3. 投影仪投射十字标记并收集对应点 --------------------------
def project_cross(projector_window_name, x, y, size=50):
    """在投影仪指定位置(Ip_x, Ip_y)投射十字标记"""
    img = np.zeros((PROJECTOR_RES[1], PROJECTOR_RES[0], 3), dtype=np.uint8)
    # 绘制十字
    cv2.line(img, (x, y - size), (x, y + size), (255, 255, 255), 2)
    cv2.line(img, (x - size, y), (x + size, y), (255, 255, 255), 2)
    # 显示在投影仪（假设投影仪为第二屏幕，窗口位置设为投影仪分辨率）
    cv2.namedWindow(projector_window_name, cv2.WINDOW_NORMAL)
    cv2.moveWindow(projector_window_name, PROJECTOR_RES[0], 0)  # 移到第二屏幕
    cv2.imshow(projector_window_name, img)
    cv2.waitKey(1000)  # 等待稳定
    return (x, y)

# 初始化相机和投影仪窗口
cap = cv2.VideoCapture(0)
cap.set(cv2.CAP_PROP_FRAME_WIDTH, img_size[0])
cap.set(cv2.CAP_PROP_FRAME_HEIGHT, img_size[1])
projector_window = "Projector Cross"

# 收集 (Pc, Ip) 对应点（Pc：相机坐标系3D点，Ip：投影仪2D点）
pc_list = []  # 3D点：[Xc, Yc, Zc]
ip_list = []  # 2D点：[Up, Vp]（投影仪坐标）

ic_list = ic_list_dict["cam_1080p_12.jpg"]

print(f"开始收集{NUM_CALIB_POINTS}个校准点...")
for i in range(NUM_CALIB_POINTS):
    
    ip_x = ((i % 6) * 4 + 4 + 2) * 54 + 95
    ip_y = ((i // 6) * 4 + 4 + 1) * 54 + 52
    ip = (ip_x, ip_y)
    ip_list.append(ip)
    ic_x, ic_y = ic_list[i]
    # 3. 根据相机内参和纸板平面方程计算Pc（相机坐标系3D点）
    # 相机内参：fx, fy, cx, cy
    fx, fy = camera_matrix[0, 0], camera_matrix[1, 1]
    cx, cy = camera_matrix[0, 2], camera_matrix[1, 2]
    
    # 由相机投影模型：u = (fx*X + cx*Z)/Z → X = Z*(u - cx)/fx
    #                  v = (fy*Y + cy*Z)/Z → Y = Z*(v - cy)/fy
    # 代入平面方程 aX + bY + cZ + d = 0 → 解Z
    u, v = ic_x, ic_y
    A = plane_eq[0] * (u - cx) / fx + plane_eq[1] * (v - cy) / fy + plane_eq[2]
    B = -plane_eq[3]
    if abs(A) < 1e-6:
        print(f"第{i+1}个点Z计算失败，跳过...")
        continue
    Zc = B / A
    Xc = Zc * (u - cx) / fx
    Yc = Zc * (v - cy) / fy
    Pc = np.array([Xc, Yc, Zc])
    pc_list.append(Pc)
    
    print(f"已收集第{i+1}/{NUM_CALIB_POINTS}个点：Pc={Pc}, Ip={ip}")

plane_eq = get_cardboard_plane("cam_1080p_14.jpg")  # [a, b, c, d]
print(f"纸板平面方程：{plane_eq[0]}X + {plane_eq[1]}Y + {plane_eq[2]}Z + {plane_eq[3]} = 0")
ic_list = ic_list_dict["cam_1080p_14.jpg"]
for i in range(NUM_CALIB_POINTS):
    ip_x = (i % 6 * 4 + 6) * 54 + 95
    ip_y = (i // 6 * 4 + 5) * 54 + 52
    ip = (ip_x, ip_y)
    ip_list.append(ip)
    ic_x, ic_y = ic_list[i]
    # 3. 根据相机内参和纸板平面方程计算Pc（相机坐标系3D点）
    # 相机内参：fx, fy, cx, cy
    fx, fy = camera_matrix[0, 0], camera_matrix[1, 1]
    cx, cy = camera_matrix[0, 2], camera_matrix[1, 2]
    
    # 由相机投影模型：u = (fx*X + cx*Z)/Z → X = Z*(u - cx)/fx
    #                  v = (fy*Y + cy*Z)/Z → Y = Z*(v - cy)/fy
    # 代入平面方程 aX + bY + cZ + d = 0 → 解Z
    u, v = ic_x, ic_y
    A = plane_eq[0] * (u - cx) / fx + plane_eq[1] * (v - cy) / fy + plane_eq[2]
    B = -plane_eq[3]
    if abs(A) < 1e-6:
        print(f"第{i+1}个点Z计算失败，跳过...")
        continue
    Zc = B / A
    Xc = Zc * (u - cx) / fx
    Yc = Zc * (v - cy) / fy
    Pc = np.array([Xc, Yc, Zc])
    pc_list.append(Pc)
    
    print(f"已收集第{i+1 + NUM_CALIB_POINTS}/{NUM_CALIB_POINTS}个点：Pc={Pc}, Ip={ip}")

X_list = []
corner_2d = np.array([[174, 107], [1096, 93],[1194, 649],[76, 638]], dtype=np.float32)
for i in range(4):
    ic_x, ic_y = corner_2d[i]
    # 3. 根据相机内参和纸板平面方程计算Pc（相机坐标系3D点）
    # 相机内参：fx, fy, cx, cy
    fx, fy = camera_matrix[0, 0], camera_matrix[1, 1]
    cx, cy = camera_matrix[0, 2], camera_matrix[1, 2]
    
    # 由相机投影模型：u = (fx*X + cx*Z)/Z → X = Z*(u - cx)/fx
    #                  v = (fy*Y + cy*Z)/Z → Y = Z*(v - cy)/fy
    # 代入平面方程 aX + bY + cZ + d = 0 → 解Z
    u, v = ic_x, ic_y
    A = plane_eq[0] * (u - cx) / fx + plane_eq[1] * (v - cy) / fy + plane_eq[2]
    B = -plane_eq[3]
    if abs(A) < 1e-6:
        print(f"第{i+1}个点Z计算失败，跳过...")
        continue
    Zc = B / A
    Xc = Zc * (u - cx) / fx
    Yc = Zc * (v - cy) / fy
    Pc = np.array([Xc, Yc, Zc])
    print(Pc)
    X_list.append(Pc)

# 释放资源
#cap.release()
cv2.destroyAllWindows()

# -------------------------- 4. 求解投影仪投影矩阵Gp（论文2.2）--------------------------
# Gp = [g11 g12 g13 g14; g21 g22 g23 g24; g31 g32 g33 g34]
# 步骤1：解前8个参数（g11-g14, g21-g24）
n_points = len(pc_list)
if n_points < 8:
    raise ValueError("校准点数量不足8个，请重新收集！")

# 构建线性方程组 B*g = 0（论文公式12）
B = []
for Pc, Ip in zip(pc_list, ip_list):
    Xc, Yc, Zc = Pc
    Up, Vp = Ip
    # 公式12：Vp*(Xc*g11 + Yc*g12 + Zc*g13 + g14) - Up*(Xc*g21 + Yc*g22 + Zc*g23 + g24) = 0
    row = [
        Vp * Xc, Vp * Yc, Vp * Zc, Vp,
        -Up * Xc, -Up * Yc, -Up * Zc, -Up
    ]
    B.append(row)
B = np.array(B, dtype=np.float32)

# SVD分解求最小二乘解
U, D, VT = np.linalg.svd(B)

g = VT[-1]  # 最小奇异值对应的特征向量（解）

g11, g12, g13, g14, g21, g22, g23, g24 = g
for i, (Pc, Ip) in enumerate(zip(pc_list, ip_list)):
    Xc, Yc, Zc = Pc
    Up, Vp = Ip
    print(i, Vp * (Xc * g11 + Yc * g12 + Zc * g13 + g14) - Up * (Xc * g21 + Yc * g22 + Zc * g23 + g24))



# 步骤2：解后4个参数（g31-g34，论文公式14）
# 公式14：Up*(Xc*g31 + Yc*g32 + Zc*g33 + g34) = Xc*g11 + Yc*g12 + Zc*g13 + g14
# 取前4个点构建方程组
if n_points < 4:
    raise ValueError("校准点数量不足4个，无法求解g31-g34！")

C = []
D = []
for i in range(len(pc_list)):
    Xc, Yc, Zc = pc_list[i]
    Up, _ = ip_list[i]
    # 方程：Up*Xc*g31 + Up*Yc*g32 + Up*Zc*g33 + Up*g34 = Xc*g11 + Yc*g12 + Zc*g13 + g14
    C.append([Up * Xc, Up * Yc, Up * Zc, Up])
    D.append(Xc * g11 + Yc * g12 + Zc * g13 + g14)
C = np.array(C, dtype=np.float32)
D = np.array(D, dtype=np.float32)

# 最小二乘求解 g3 = [g31, g32, g33, g34]
g3, residuals, _, _ = np.linalg.lstsq(C, D, rcond=None)
g31, g32, g33, g34 = g3
print(residuals)
for i, (Pc, Ip) in enumerate(zip(pc_list, ip_list)):
    Xc, Yc, Zc = Pc
    Up, Vp = Ip
    print(i, Up * (Xc * g31 + Yc * g32 + Zc * g33 + g34) - (Xc * g11 + Yc * g12 + Zc * g13 + g14))



# 构建完整投影矩阵Gp
Gp = np.array([
    [g11, g12, g13, g14],
    [g21, g22, g23, g24],
    [g31, g32, g33, g34]
], dtype=np.float32)

np.save("G.npy", Gp)
# 保存Gp到文件
with open(SAVE_GP_PATH, "w") as f:
    yaml.dump({"Gp": Gp.tolist()}, f)

print("投影仪-相机对校准完成！")
print(f"投影矩阵 Gp:\n{Gp}")

G1, G2, G3 = Gp[0], Gp[1], Gp[2]

for i in range(4):
    Xi = X_list[i]
    Xi_hom = np.append(Xi, 1)  # 转换为齐次坐标 [X, Y, Z, 1]
    alpha_pred = (G1 @ Xi_hom) / (G3 @ Xi_hom + 1e-6)
    beta_pred = (G2 @ Xi_hom) / (G3 @ Xi_hom + 1e-6)
    print(i, alpha_pred, beta_pred, Xi)

for i in range(NUM_CALIB_POINTS):
    Xi = pc_list[i + NUM_CALIB_POINTS]
    Xi_hom = np.append(Xi, 1)  # 转换为齐次坐标 [X, Y, Z, 1]
    alpha_pred = (G1 @ Xi_hom) / (G3 @ Xi_hom + 1e-6)
    beta_pred = (G2 @ Xi_hom) / (G3 @ Xi_hom + 1e-6)
    print(i, alpha_pred, beta_pred, ip_list[i + NUM_CALIB_POINTS], Xi)